/**
 * Shortcode Converter - Billboard Maker
 * Shortcode conversion methods
 * Part of CF7TextEditor system
 */

// These methods will be mixed into the CF7TextEditor class
export const shortcodeConverterMethods = {
    convertShortcodeToButton(shortcode, className, callback) {
        const walker = document.createTreeWalker(
            this.container,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let textNode;
        while (textNode = walker.nextNode()) {
            if (textNode.textContent.includes(shortcode)) {
                const parent = textNode.parentElement;
                const text = this.extractShortcodeAttribute(textNode.textContent, 'text') || 'Button';

                const button = document.createElement('button');
                button.textContent = text;
                button.className = className;
                button.type = 'button'; // Prevent form submission in WordPress

                // WordPress-compatible event handling
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    callback();
                });

                parent.appendChild(button);

                textNode.textContent = '';
                break;
            }
        }
    },

    convertShortcodeToSelect(shortcode, className, options, callback) {
        const walker = document.createTreeWalker(this.container, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        while (textNode = walker.nextNode()) {
            if (textNode.textContent.includes(shortcode)) {
                const parent = textNode.parentElement;
                const select = document.createElement('select');
                select.className = className;

                options.forEach(option => {
                    const optionEl = document.createElement('option');
                    optionEl.value = option.value;
                    optionEl.textContent = option.text;
                    select.appendChild(optionEl);
                });

                select.onchange = (e) => callback(e.target.value);
                parent.appendChild(select);

                // Track controls based on shortcode type
                if (shortcode.includes('font-family')) {
                    this.fontControls.fontFamily = select;
                } else if (shortcode.includes('font-size')) {
                    this.fontControls.fontSize = select;
                } else if (shortcode.includes('background-category')) {
                    this.backgroundControls.category = select;
                } else if (shortcode.includes('background-template')) {
                    this.backgroundControls.template = select;
                }

                textNode.textContent = '';
                break;
            }
        }
    },

    // CF7 Font Preview Dropdown - Shows each font in its own typeface
    createFontPreviewDropdown(shortcode, className, options, callback) {
        const walker = document.createTreeWalker(this.container, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        while (textNode = walker.nextNode()) {
            if (textNode.textContent.includes(shortcode)) {
                const parent = textNode.parentElement;

                // Create custom dropdown container
                const dropdownContainer = document.createElement('div');
                dropdownContainer.className = 'cf7-font-preview-dropdown';

                // Create button that shows selected font
                const selectButton = document.createElement('button');
                selectButton.type = 'button';
                selectButton.className = 'cf7-font-preview-button';
                selectButton.innerHTML = `
                    <span class="cf7-selected-font">Select Font</span>
                    <span class="cf7-dropdown-arrow">▼</span>
                `;

                // Create dropdown list
                const dropdownList = document.createElement('ul');
                dropdownList.className = 'cf7-font-preview-list cf7-hidden';
                dropdownList.setAttribute('role', 'listbox');

                // Add options to dropdown
                options.forEach((option, index) => {
                    const listItem = document.createElement('li');
                    listItem.className = 'cf7-font-preview-option';
                    listItem.setAttribute('role', 'option');
                    listItem.setAttribute('data-value', option.value);
                    listItem.style.fontFamily = option.family || option.value;
                    listItem.textContent = option.text;

                    // Handle option selection
                    listItem.addEventListener('click', () => {
                        // Update button text and font
                        const selectedSpan = selectButton.querySelector('.cf7-selected-font');
                        selectedSpan.textContent = option.text;
                        selectedSpan.style.fontFamily = option.family || option.value;

                        // Remove selected class from all options
                        dropdownList.querySelectorAll('.cf7-font-preview-option').forEach(opt => {
                            opt.classList.remove('cf7-selected');
                        });

                        // Add selected class to current option
                        listItem.classList.add('cf7-selected');

                        // Close dropdown
                        dropdownList.classList.add('cf7-hidden');
                        selectButton.setAttribute('aria-expanded', 'false');

                        // Call callback
                        callback(option.value);
                    });

                    dropdownList.appendChild(listItem);
                });

                // Handle dropdown toggle
                selectButton.addEventListener('click', () => {
                    const isHidden = dropdownList.classList.contains('cf7-hidden');
                    dropdownList.classList.toggle('cf7-hidden', !isHidden);
                    selectButton.setAttribute('aria-expanded', isHidden);
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', (e) => {
                    if (!dropdownContainer.contains(e.target)) {
                        dropdownList.classList.add('cf7-hidden');
                        selectButton.setAttribute('aria-expanded', 'false');
                    }
                });

                // Assemble dropdown
                dropdownContainer.appendChild(selectButton);
                dropdownContainer.appendChild(dropdownList);
                parent.appendChild(dropdownContainer);

                // Store reference for font controls
                this.fontControls.fontFamily = dropdownContainer;

                textNode.textContent = '';
                break;
            }
        }
    },

    convertShortcodeToInput(shortcode, className, type, defaultValue, callback) {
        const walker = document.createTreeWalker(this.container, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        while (textNode = walker.nextNode()) {
            if (textNode.textContent.includes(shortcode)) {
                const parent = textNode.parentElement;
                const input = document.createElement('input');
                input.type = type;
                input.className = className;
                input.value = defaultValue;

                input.addEventListener('input', (e) => callback(e.target.value));
                input.addEventListener('change', (e) => callback(e.target.value));

                parent.appendChild(input);

                // Track controls based on shortcode type
                if (shortcode.includes('font-size')) {
                    this.fontControls.fontSize = input;
                }

                textNode.textContent = '';
                break;
            }
        }
    },

    convertShortcodeToToggle(shortcode, className, callback) {
        const walker = document.createTreeWalker(this.container, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        while (textNode = walker.nextNode()) {
            if (textNode.textContent.includes(shortcode)) {
                const parent = textNode.parentElement;
                const button = document.createElement('button');
                button.type = 'button';
                button.className = className;

                // Set button text based on shortcode type
                if (shortcode.includes('font-bold')) {
                    button.innerHTML = '<strong>B</strong>';
                    this.fontControls.bold = button;
                } else if (shortcode.includes('font-italic')) {
                    button.innerHTML = '<em>I</em>';
                    this.fontControls.italic = button;
                } else if (shortcode.includes('align-left')) {
                    button.innerHTML = '⬅';
                    this.fontControls.alignLeft = button;
                } else if (shortcode.includes('align-center')) {
                    button.innerHTML = '⬌';
                    this.fontControls.alignCenter = button;
                } else if (shortcode.includes('align-right')) {
                    button.innerHTML = '➡';
                    this.fontControls.alignRight = button;
                } else if (shortcode.includes('align-justify')) {
                    button.innerHTML = '⬍';
                    this.fontControls.alignJustify = button;
                } else {
                    button.textContent = 'Toggle';
                }

                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    callback();
                });

                parent.appendChild(button);
                textNode.textContent = '';
                break;
            }
        }
    },

    convertShortcodeToColorPicker(shortcode, className, defaultValue, callback) {
        const walker = document.createTreeWalker(this.container, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        while (textNode = walker.nextNode()) {
            if (textNode.textContent.includes(shortcode)) {
                const parent = textNode.parentElement;
                const input = document.createElement('input');
                input.type = 'color';
                input.className = className;
                input.value = defaultValue;

                input.addEventListener('input', (e) => callback(e.target.value));
                input.addEventListener('change', (e) => callback(e.target.value));

                parent.appendChild(input);

                // Track controls based on shortcode type
                if (shortcode.includes('font-color')) {
                    this.fontControls.color = input;
                }

                textNode.textContent = '';
                break;
            }
        }
    },

    convertShortcodeToRangeSlider(shortcode, className, min, max, defaultValue, callback, valueDisplayId, unit) {
        const walker = document.createTreeWalker(this.container, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        while (textNode = walker.nextNode()) {
            if (textNode.textContent.includes(shortcode)) {
                const parent = textNode.parentElement;
                const input = document.createElement('input');
                input.type = 'range';
                input.className = className;
                input.min = min;
                input.max = max;
                input.value = defaultValue;

                input.addEventListener('input', (e) => {
                    const value = e.target.value;
                    callback(value);

                    // Update value display if provided
                    if (valueDisplayId) {
                        const display = document.getElementById(valueDisplayId);
                        if (display) {
                            display.textContent = value + (unit || '');
                        }
                    }
                });

                parent.appendChild(input);
                textNode.textContent = '';
                break;
            }
        }
    },

    extractShortcodeAttribute(text, attribute) {
        const regex = new RegExp(`${attribute}=["']([^"']+)["']`);
        const match = text.match(regex);
        return match ? match[1] : null;
    }
};
