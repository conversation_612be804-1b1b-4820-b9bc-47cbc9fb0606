/**
 * CF7TextEditor - Core Class
 * Billboard Maker - Main Editor Class
 * Part of CF7TextEditor system
 */

class CF7TextEditor {
    constructor(container) {
        // Core DOM elements
        this.container = container;
        this.canvas = container.querySelector('#cf7-canvas');
        this.elementsContainer = container.querySelector('#cf7-elements');

        // State management
        this.selectedElement = null;
        this.elementCounter = 0;

        // Interaction data
        this.dragData = {
            isDragging: false,
            startX: 0,
            startY: 0,
            elementX: 0,
            elementY: 0,
            element: null
        };
        this.resizeData = {
            isResizing: false,
            originalMouseX: 0,
            originalMouseY: 0,
            originalWidth: 0,
            originalHeight: 0,
            originalX: 0,
            originalY: 0,
            handle: null,
            element: null
        };

        // Control references
        this.fontControls = {};
        this.backgroundControls = {};

        // Initialize background templates
        this.backgroundTemplates = this.initializeBackgroundTemplates();

        // Initialize the editor
        this.init();
    }
}