export const backgroundManagerMethods = {
        openBackgroundModal() {
            if (!this.modal.element) return;

            // Store the currently focused element
            this.modal.focusedElementBeforeModal = document.activeElement;

            // Show the modal
            this.modal.element.showModal();

            // WordPress compatibility fix - Force center positioning and content visibility
            setTimeout(() => {
                if (this.modal.element) {
                    // Fix modal positioning
                    this.modal.element.style.position = 'fixed';
                    this.modal.element.style.top = '50%';
                    this.modal.element.style.left = '50%';
                    this.modal.element.style.transform = 'translate(-50%, -50%)';
                    this.modal.element.style.zIndex = '999999';
                    this.modal.element.style.margin = '0';
                    this.modal.element.style.inset = 'auto';
                    this.modal.element.style.visibility = 'visible';
                    this.modal.element.style.opacity = '1';

                    // Fix modal content visibility
                    const modalContent = this.modal.element.querySelector('.cf7-modal-content');
                    if (modalContent) {
                        modalContent.style.display = 'flex';
                        modalContent.style.flexDirection = 'column';
                        modalContent.style.height = '100%';
                        modalContent.style.width = '100%';
                    }

                    // Fix modal body visibility
                    const modalBody = this.modal.element.querySelector('.cf7-modal-body');
                    if (modalBody) {
                        modalBody.style.display = 'block';
                        modalBody.style.visibility = 'visible';
                        modalBody.style.opacity = '1';
                        modalBody.style.overflow = 'auto';
                    }

                    // Fix category grid visibility
                    const categoryGrid = this.modal.element.querySelector('.cf7-category-grid');
                    if (categoryGrid) {
                        categoryGrid.style.display = 'grid';
                        categoryGrid.style.visibility = 'visible';
                        categoryGrid.style.opacity = '1';
                    }
                }
            }, 10);

            // Reset modal state
            this.resetModalState();

            // Focus the first focusable element in the modal
            this.focusFirstModalElement();

            // Populate categories if not already done
            this.populateCategories();
        },

        closeBackgroundModal() {
            if (!this.modal.element) return;

            // Close the modal
            this.modal.element.close();

            // Return focus to the trigger button
            if (this.modal.focusedElementBeforeModal) {
                this.modal.focusedElementBeforeModal.focus();
            }

            // Reset modal state
            this.resetModalState();
        },

        resetModalState() {
            // Reset selections
            this.modal.selectedCategory = null;
            this.modal.selectedTemplate = null;

            // WordPress-compatible step reset - Show category step, hide template step
            const categoryStep = document.getElementById('cf7-step-category');
            const templateStep = document.getElementById('cf7-step-template');

            // Ensure category step is fully visible
            if (categoryStep) {
                categoryStep.style.display = 'block';
                categoryStep.style.visibility = 'visible';
                categoryStep.style.opacity = '1';
                categoryStep.style.position = 'relative';
                categoryStep.style.left = 'auto';
                categoryStep.style.height = 'auto';
                categoryStep.style.overflow = 'visible';
            }

            // Ensure template step is fully hidden
            if (templateStep) {
                templateStep.style.display = 'none';
                templateStep.style.visibility = 'hidden';
                templateStep.style.opacity = '0';
                templateStep.style.position = 'absolute';
                templateStep.style.left = '-9999px';
                templateStep.style.height = '0';
                templateStep.style.overflow = 'hidden';
            }

            // Disable apply button
            if (this.modal.apply) {
                this.modal.apply.disabled = true;
            }

            // Clear selections
            this.clearModalSelections();
        },

        clearModalSelections() {
            // Clear category selections
            const categoryItems = document.querySelectorAll('.cf7-category-item');
            categoryItems.forEach(item => item.classList.remove('selected'));

            // Clear template selections
            const templateItems = document.querySelectorAll('.cf7-template-item');
            templateItems.forEach(item => item.classList.remove('selected'));
        },

        focusFirstModalElement() {
            const firstFocusable = this.modal.element.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            if (firstFocusable) {
                firstFocusable.focus();
            }
        },

        setupModalContent() {
            // Setup back button
            const backButton = document.getElementById('cf7-back-to-categories');
            if (backButton) {
                backButton.addEventListener('click', () => this.showCategoryStep());
            }
        },

        setupModalKeyboardNavigation() {
            if (!this.modal.element) return;

            this.modal.element.addEventListener('keydown', (e) => {
                // Close modal on Escape key
                if (e.key === 'Escape') {
                    e.preventDefault();
                    this.closeBackgroundModal();
                    return;
                }

                // Handle Tab key for focus trapping
                if (e.key === 'Tab') {
                    this.trapFocus(e);
                }
            });
        },

        trapFocus(e) {
            const focusableElements = this.modal.element.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );

            const firstFocusable = focusableElements[0];
            const lastFocusable = focusableElements[focusableElements.length - 1];

            if (e.shiftKey) {
                // Shift + Tab
                if (document.activeElement === firstFocusable) {
                    e.preventDefault();
                    lastFocusable.focus();
                }
            } else {
                // Tab
                if (document.activeElement === lastFocusable) {
                    e.preventDefault();
                    firstFocusable.focus();
                }
            }
        },

        // Modal Content Population Methods
        populateCategories() {
            const categoryGrid = document.getElementById('cf7-category-grid');
            if (!categoryGrid) return;

            // Clear existing categories
            categoryGrid.innerHTML = '';

            // Create category items
            const categories = Object.keys(this.backgroundTemplates);
            categories.forEach((category, index) => {
                const categoryItem = document.createElement('div');
                categoryItem.className = 'cf7-category-item';
                categoryItem.setAttribute('role', 'radio');
                categoryItem.setAttribute('aria-checked', 'false');
                categoryItem.setAttribute('tabindex', index === 0 ? '0' : '-1');
                categoryItem.dataset.category = category;

                categoryItem.innerHTML = `
                    <h4>${category}</h4>
                    <p>${this.backgroundTemplates[category].length} templates</p>
                `;

                // Add click handler
                categoryItem.addEventListener('click', () => this.selectCategory(category, categoryItem));

                // Add keyboard handler
                categoryItem.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.selectCategory(category, categoryItem);
                    }
                    this.handleGridNavigation(e, categoryGrid);
                });

                categoryGrid.appendChild(categoryItem);
            });
        },

        selectCategory(category, categoryElement) {
            // Update selection state
            this.modal.selectedCategory = category;
            this.modal.selectedTemplate = null;

            // Update UI
            this.clearModalSelections();
            categoryElement.classList.add('selected');
            categoryElement.setAttribute('aria-checked', 'true');

            // Show template step
            this.showTemplateStep(category);
        },

        showTemplateStep(category) {
            const categoryStep = document.getElementById('cf7-step-category');
            const templateStep = document.getElementById('cf7-step-template');

            // WordPress-compatible step switching - Hide category step completely
            if (categoryStep) {
                categoryStep.style.display = 'none';
                categoryStep.style.visibility = 'hidden';
                categoryStep.style.opacity = '0';
                categoryStep.style.position = 'absolute';
                categoryStep.style.left = '-9999px';
                categoryStep.style.height = '0';
                categoryStep.style.overflow = 'hidden';
            }

            // Show template step completely
            if (templateStep) {
                templateStep.style.display = 'block';
                templateStep.style.visibility = 'visible';
                templateStep.style.opacity = '1';
                templateStep.style.position = 'relative';
                templateStep.style.left = 'auto';
                templateStep.style.height = 'auto';
                templateStep.style.overflow = 'visible';
            }

            // Populate templates
            this.populateTemplates(category);

            // Focus the back button
            const backButton = document.getElementById('cf7-back-to-categories');
            if (backButton) {
                setTimeout(() => backButton.focus(), 100);
            }
        },

        showCategoryStep() {
            const categoryStep = document.getElementById('cf7-step-category');
            const templateStep = document.getElementById('cf7-step-template');

            // WordPress-compatible step switching - Show category step completely
            if (categoryStep) {
                categoryStep.style.display = 'block';
                categoryStep.style.visibility = 'visible';
                categoryStep.style.opacity = '1';
                categoryStep.style.position = 'relative';
                categoryStep.style.left = 'auto';
                categoryStep.style.height = 'auto';
                categoryStep.style.overflow = 'visible';
            }

            // Hide template step completely
            if (templateStep) {
                templateStep.style.display = 'none';
                templateStep.style.visibility = 'hidden';
                templateStep.style.opacity = '0';
                templateStep.style.position = 'absolute';
                templateStep.style.left = '-9999px';
                templateStep.style.height = '0';
                templateStep.style.overflow = 'hidden';
            }

            // Reset template selection
            this.modal.selectedTemplate = null;
            if (this.modal.apply) {
                this.modal.apply.disabled = true;
            }

            // Focus the selected category
            const selectedCategory = document.querySelector('.cf7-category-item.selected');
            if (selectedCategory) {
                setTimeout(() => selectedCategory.focus(), 100);
            }
        },

        populateTemplates(category) {
            const templateGrid = document.getElementById('cf7-template-grid');
            if (!templateGrid) return;

            // Clear existing templates
            templateGrid.innerHTML = '';

            // Get templates for category
            const templates = this.backgroundTemplates[category] || [];

            // Create template items
            templates.forEach((template, index) => {
                const templateItem = document.createElement('div');
                templateItem.className = 'cf7-template-item';
                templateItem.setAttribute('role', 'radio');
                templateItem.setAttribute('aria-checked', 'false');
                templateItem.setAttribute('tabindex', index === 0 ? '0' : '-1');
                templateItem.dataset.templateUrl = template.url;

                templateItem.innerHTML = `
                    <img src="${template.url}" alt="${template.name}" loading="lazy">
                    <h4>${template.name}</h4>
                `;

                // Add click handler
                templateItem.addEventListener('click', () => this.selectTemplate(template.url, templateItem));

                // Add keyboard handler
                templateItem.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.selectTemplate(template.url, templateItem);
                    }
                    this.handleGridNavigation(e, templateGrid);
                });

                templateGrid.appendChild(templateItem);
            });
        },

        selectTemplate(templateUrl, templateElement) {
            // Update selection state
            this.modal.selectedTemplate = templateUrl;

            // Update UI
            const templateItems = document.querySelectorAll('.cf7-template-item');
            templateItems.forEach(item => {
                item.classList.remove('selected');
                item.setAttribute('aria-checked', 'false');
            });

            templateElement.classList.add('selected');
            templateElement.setAttribute('aria-checked', 'true');

            // Enable apply button
            if (this.modal.apply) {
                this.modal.apply.disabled = false;
            }
        },

        handleGridNavigation(e, grid) {
            const items = Array.from(grid.children);
            const currentIndex = items.indexOf(document.activeElement);
            let newIndex = currentIndex;

            switch (e.key) {
                case 'ArrowRight':
                    e.preventDefault();
                    newIndex = (currentIndex + 1) % items.length;
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    newIndex = (currentIndex - 1 + items.length) % items.length;
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    // Move to next row (approximate)
                    newIndex = Math.min(currentIndex + 3, items.length - 1);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    // Move to previous row (approximate)
                    newIndex = Math.max(currentIndex - 3, 0);
                    break;
                case 'Home':
                    e.preventDefault();
                    newIndex = 0;
                    break;
                case 'End':
                    e.preventDefault();
                    newIndex = items.length - 1;
                    break;
            }

            if (newIndex !== currentIndex && items[newIndex]) {
                // Update tabindex
                items.forEach((item, index) => {
                    item.setAttribute('tabindex', index === newIndex ? '0' : '-1');
                });
                items[newIndex].focus();
            }
        },

        applySelectedBackground() {
            if (this.modal.selectedTemplate) {
                this.setBackgroundImage(this.modal.selectedTemplate);
                this.closeBackgroundModal();
            }
        },

        setBackgroundImage(imageUrl) {
            if (!this.canvas) return;

            // Remove existing background image
            this.clearBackground();

            // Create background image element
            const backgroundImg = document.createElement('img');
            backgroundImg.src = imageUrl;
            backgroundImg.className = 'cf7-background-image';
            backgroundImg.style.position = 'absolute';
            backgroundImg.style.top = '0';
            backgroundImg.style.left = '0';
            backgroundImg.style.width = '100%';
            backgroundImg.style.height = '100%';
            backgroundImg.style.objectFit = 'cover';
            backgroundImg.style.zIndex = '-1';
            backgroundImg.style.pointerEvents = 'none';

            // Add to canvas
            this.canvas.appendChild(backgroundImg);

            // Store reference
            this.canvas.dataset.backgroundImage = imageUrl;
        },

        clearBackground() {
            if (!this.canvas) return;

            // Remove existing background image
            const existingBg = this.canvas.querySelector('.cf7-background-image');
            if (existingBg) {
                existingBg.remove();
            }

            // Clear background data
            delete this.canvas.dataset.backgroundImage;

            // Reset canvas background
            this.canvas.style.backgroundImage = 'none';
        },

        deleteElement(element) {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
            if (this.selectedElement === element) {
                this.selectedElement = null;
                this.resetFontControls();
            }
        }
}