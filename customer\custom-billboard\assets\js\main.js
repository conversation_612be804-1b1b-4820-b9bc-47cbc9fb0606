/**
 * Main Entry Point - Billboard Maker
 * Imports and initializes all modules
 * Part of CF7TextEditor system
 */

// Import all modules
import CF7TextEditor from './modules/core/CF7TextEditor.js';
import { initializationMethods } from './modules/core/initialization.js';
import { backgroundControlMethods } from './modules/background/backgroundControls.js';
import { backgroundManagerMethods } from './modules/background/backgroundManager.js';
import { fontControlMethods } from './modules/fonts/fontControls.js';
import { fontManagerMethods } from './modules/fonts/fontManager.js';
import { shortcodeConverterMethods } from './modules/elements/shortcodeConverter.js';
import { elementManagerMethods } from './modules/elements/elementManager.js';
import { dragDropMethods } from './modules/elements/dragDrop.js';
import { canvasManagerMethods } from './modules/canvas/canvasManager.js';

// Import utility modules (these execute immediately)
import './modules/utils/utilities.js';
import './modules/utils/checkout.js';

// Mix all methods into the CF7TextEditor class
Object.assign(CF7TextEditor.prototype, initializationMethods);
Object.assign(CF7TextEditor.prototype, backgroundControlMethods);
Object.assign(CF7TextEditor.prototype, backgroundManagerMethods);
Object.assign(CF7TextEditor.prototype, fontControlMethods);
Object.assign(CF7TextEditor.prototype, fontManagerMethods);
Object.assign(CF7TextEditor.prototype, shortcodeConverterMethods);
Object.assign(CF7TextEditor.prototype, elementManagerMethods);
Object.assign(CF7TextEditor.prototype, dragDropMethods);
Object.assign(CF7TextEditor.prototype, canvasManagerMethods);

// Export for global access
window.CF7TextEditor = CF7TextEditor;

// Export for module systems
export default CF7TextEditor;

// Debug: Test if all imports loaded successfully
console.log('✅ All modules imported successfully!');
console.log('CF7TextEditor:', typeof CF7TextEditor);
console.log('initializationMethods:', typeof initializationMethods);
console.log('backgroundControlMethods:', typeof backgroundControlMethods);
console.log('backgroundManagerMethods:', typeof backgroundManagerMethods);
console.log('fontControlMethods:', typeof fontControlMethods);
console.log('fontManagerMethods:', typeof fontManagerMethods);
console.log('shortcodeConverterMethods:', typeof shortcodeConverterMethods);
console.log('elementManagerMethods:', typeof elementManagerMethods);
console.log('dragDropMethods:', typeof dragDropMethods);
console.log('canvasManagerMethods:', typeof canvasManagerMethods);
