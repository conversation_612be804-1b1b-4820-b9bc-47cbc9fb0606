/**
 * Element Manager - Billboard Maker
 * Element management methods
 * Part of CF7TextEditor system
 */

// These methods will be mixed into the CF7TextEditor class
export const elementManagerMethods = {
    addTextElement() {
        this.elementCounter++;

        // Create container element (non-editable)
        const textElement = document.createElement('div');
        textElement.className = 'cf7-draggable-text';
        textElement.style.left = '50px';
        textElement.style.top = '50px';
        textElement.style.width = '150px';
        textElement.style.height = '40px';
        textElement.setAttribute('data-element-id', `text-${this.elementCounter}`);

        // Create separate editable content area
        const editableContent = document.createElement('div');
        editableContent.className = 'cf7-editable-content';
        editableContent.contentEditable = true;
        editableContent.textContent = `Text ${this.elementCounter}`;
        editableContent.setAttribute('data-placeholder', 'Enter text...');

        // Add delete button with enhanced functionality
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'cf7-delete-btn';
        deleteBtn.innerHTML = '×';
        deleteBtn.title = 'Delete text element';
        deleteBtn.setAttribute('aria-label', 'Delete text element');
        deleteBtn.setAttribute('contenteditable', 'false'); // Prevent editing

        // Enhanced event handling for delete button
        deleteBtn.onmousedown = (e) => {
            e.stopPropagation(); // Prevent drag start
            e.preventDefault();
        };

        deleteBtn.onclick = (e) => {
            e.stopPropagation();
            e.preventDefault();
            this.deleteElement(textElement);
        };

        // Add resize handles
        this.addResizeHandles(textElement);

        // Append elements in correct order
        textElement.appendChild(editableContent);
        textElement.appendChild(deleteBtn);

        // Setup enhanced element events
        this.setupElementEvents(textElement);
        this.setupTextEditingEvents(textElement, editableContent);

        this.elementsContainer.appendChild(textElement);
        this.selectElement(textElement);
    },

    addResizeHandles(element) {
        const handles = ['nw', 'ne', 'sw', 'se'];
        handles.forEach(direction => {
            const handle = document.createElement('div');
            handle.className = `cf7-resize-handle cf7-resize-${direction}`;
            handle.setAttribute('contenteditable', 'false'); // Prevent editing
            handle.setAttribute('data-no-edit', 'true'); // Additional protection

            // Enhanced event handling
            handle.addEventListener('mousedown', (e) => {
                e.stopPropagation(); // Prevent text selection
                this.handleResizeDown(e, element, direction);
            });

            // Prevent text editing interference
            handle.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
            });

            element.appendChild(handle);
        });
    },

    setupTextEditingEvents(textElement, editableContent) {
        // Store reference to editable content
        textElement._editableContent = editableContent;

        // Enhanced text editing event handling
        editableContent.addEventListener('focus', (e) => {
            e.stopPropagation();
            textElement.classList.add('cf7-editing');
            this.selectElement(textElement);
        });

        editableContent.addEventListener('blur', (e) => {
            e.stopPropagation();
            textElement.classList.remove('cf7-editing');
            this.preserveElementStructure(textElement);
        });

        // CF7 Pattern: Conditional event handling for drag vs edit
        editableContent.addEventListener('mousedown', (e) => {
            // Only stop propagation if actively editing (focused)
            if (document.activeElement === editableContent) {
                e.stopPropagation();
            } else {
                // Allow drag to work by not stopping propagation
                // This follows CF7 pattern of conditional event handling
            }
        });

        // Handle text changes with structure preservation
        editableContent.addEventListener('input', (e) => {
            this.handleTextInput(textElement, editableContent);
        });

        // Handle keyboard events
        editableContent.addEventListener('keydown', (e) => {
            this.handleTextKeydown(e, textElement, editableContent);
        });

        // CF7 Pattern: Double click to enter edit mode
        textElement.addEventListener('dblclick', (e) => {
            if (!e.target.classList.contains('cf7-editable-content')) {
                e.preventDefault();
                e.stopPropagation();
                editableContent.focus();
                // Set cursor at end of text
                const range = document.createRange();
                const selection = window.getSelection();
                range.selectNodeContents(editableContent);
                range.collapse(false);
                selection.removeAllRanges();
                selection.addRange(range);
            }
        });
    },

    handleTextInput(textElement, editableContent) {
        // Preserve element structure during text input
        this.preserveElementStructure(textElement);

        // Handle empty content
        if (!editableContent.textContent.trim()) {
            editableContent.setAttribute('data-placeholder', 'Enter text...');
        } else {
            editableContent.removeAttribute('data-placeholder');
        }
    },

    handleTextKeydown(e, textElement, editableContent) {
        // Handle special keys
        if (e.key === 'Enter') {
            e.preventDefault(); // Prevent line breaks in single-line text
        } else if (e.key === 'Escape') {
            editableContent.blur();
        }
    },

    preserveElementStructure(textElement) {
        // Ensure the element structure remains intact
        const editableContent = textElement.querySelector('.cf7-editable-content');
        const deleteBtn = textElement.querySelector('.cf7-delete-btn');
        const resizeHandles = textElement.querySelectorAll('.cf7-resize-handle');

        // If any essential elements are missing, recreate them
        if (!deleteBtn) {
            const newDeleteBtn = document.createElement('button');
            newDeleteBtn.className = 'cf7-delete-btn';
            newDeleteBtn.innerHTML = '×';
            newDeleteBtn.onclick = (e) => {
                e.stopPropagation();
                this.deleteElement(textElement);
            };
            textElement.appendChild(newDeleteBtn);
        }

        if (resizeHandles.length < 4) {
            // Remove existing handles and recreate all
            resizeHandles.forEach(handle => handle.remove());
            this.addResizeHandles(textElement);
        }
    },

    setupElementEvents(element) {
        element.addEventListener('mousedown', (e) => this.handleMouseDown(e, element));

        // Global mouse events for dragging and resizing
        if (!this.globalEventsSetup) {
            document.addEventListener('mousemove', (e) => {
                this.handleMouseMove(e);
                this.handleResizeMove(e);
            });

            document.addEventListener('mouseup', () => {
                this.handleMouseUp();
                this.handleResizeUp();
            });

            this.globalEventsSetup = true;
        }
    }
};
