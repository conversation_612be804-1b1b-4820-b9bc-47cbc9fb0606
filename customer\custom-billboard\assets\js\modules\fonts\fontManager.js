/**
 * Font Manager - Billboard Maker
 * Font management methods
 * Part of CF7TextEditor system
 */

// These methods will be mixed into the CF7TextEditor class
export const fontManagerMethods = {
    updateSelectedFont(property, value) {
        if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) return;

        // Apply font styles to the editable content area
        const editableContent = this.selectedElement.querySelector('.cf7-editable-content');
        if (editableContent) {
            editableContent.style[property] = value;
        } else {
            // Fallback for older elements
            this.selectedElement.style[property] = value;
        }
    },

    toggleSelectedFont(property, activeValue, inactiveValue) {
        // Get the button that was clicked for visual feedback
        let button = null;
        if (property === 'fontWeight') button = this.fontControls.bold;
        if (property === 'fontStyle') button = this.fontControls.italic;

        // If no element is selected, just toggle button visual state
        if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) {
            if (button) {
                button.classList.toggle('cf7-active');
                console.log('No element selected, toggled button state:', button.classList.contains('cf7-active'));
            }
            return;
        }

        const editableContent = this.selectedElement.querySelector('.cf7-editable-content');
        const targetElement = editableContent || this.selectedElement;

        const currentValue = targetElement.style[property] ||
                           window.getComputedStyle(targetElement)[property];
        const newValue = (currentValue === activeValue) ? inactiveValue : activeValue;

        console.log(`Toggling ${property}: ${currentValue} -> ${newValue}`);
        targetElement.style[property] = newValue;

        // Don't call updateFontControlsFromElement as it might override our button state
        // Instead, manually update the button state
        if (button) {
            const isActive = (newValue === activeValue);
            button.classList.toggle('cf7-active', isActive);
            console.log(`Button ${property} active state:`, isActive);
        }
    },

    // CF7 Compatible: Set text alignment for selected text element
    setTextAlignment(alignment) {
        console.log('setTextAlignment called with:', alignment);
        console.log('Selected element:', this.selectedElement);

        // Get the alignment buttons for visual feedback
        const alignButtons = {
            left: this.fontControls.alignLeft,
            center: this.fontControls.alignCenter,
            right: this.fontControls.alignRight,
            justify: this.fontControls.alignJustify
        };

        console.log('Alignment buttons:', alignButtons);

        // If no element is selected, just update button visual state
        if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) {
            // Clear all alignment button active states
            Object.values(alignButtons).forEach(btn => {
                if (btn) btn.classList.remove('cf7-active');
            });
            // Set the clicked button as active
            if (alignButtons[alignment]) {
                alignButtons[alignment].classList.add('cf7-active');
            }
            return;
        }

        const editableContent = this.selectedElement.querySelector('.cf7-editable-content');
        const targetElement = editableContent || this.selectedElement;

        // Apply text alignment with !important to override any conflicting styles
        targetElement.style.setProperty('text-align', alignment, 'important');
        console.log(`Text alignment set to: ${alignment}`);

        // For justify, add additional properties
        if (alignment === 'justify') {
            targetElement.style.setProperty('text-align-last', 'left', 'important');
            targetElement.style.setProperty('word-spacing', 'normal', 'important');
            targetElement.style.setProperty('letter-spacing', 'normal', 'important');
            console.log('Justify alignment applied with additional properties');
        } else {
            // Remove justify-specific properties for other alignments
            targetElement.style.removeProperty('text-align-last');
        }

        console.log('Element computed textAlign:', window.getComputedStyle(targetElement).textAlign);
        console.log('Element style textAlign:', targetElement.style.textAlign);

        // Update button visual states - only one alignment can be active at a time
        console.log('Updating button states for alignment:', alignment);
        Object.entries(alignButtons).forEach(([align, btn]) => {
            if (btn) {
                const shouldBeActive = align === alignment;
                console.log(`Button ${align}: ${shouldBeActive ? 'ACTIVE' : 'inactive'}`);
                if (shouldBeActive) {
                    btn.classList.add('cf7-active');
                } else {
                    btn.classList.remove('cf7-active');
                }
            }
        });
    },

    // Debug method to test button toggle
    testBoldButtonToggle() {
        if (this.fontControls.bold) {
            console.log('Testing Bold button toggle...');
            console.log('Before toggle - has cf7-active:', this.fontControls.bold.classList.contains('cf7-active'));
            this.fontControls.bold.classList.toggle('cf7-active');
            console.log('After toggle - has cf7-active:', this.fontControls.bold.classList.contains('cf7-active'));
            console.log('Button classes:', this.fontControls.bold.className);
        } else {
            console.log('Bold button not found!');
        }
    },

    // Text Shadow Methods - Following CF7 patterns
    toggleTextShadow() {
        if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) return;

        const shadowToggle = this.container.querySelector('.cf7-btn-shadow');
        if (!shadowToggle) return;

        const isActive = shadowToggle.classList.contains('cf7-active');
        const editableContent = this.selectedElement.querySelector('.cf7-editable-content');
        const targetElement = editableContent || this.selectedElement;

        if (isActive) {
            // Remove text shadow
            targetElement.style.textShadow = 'none';
            this.selectedElement.classList.remove('cf7-has-shadow');
            shadowToggle.classList.remove('cf7-active');

            // Mark shadow as not applied
            if (this.selectedElement.shadowConfig) {
                this.selectedElement.shadowConfig.applied = false;
            }
        } else {
            // Apply text shadow with current settings
            this.applyTextShadowToSelected();
            this.selectedElement.classList.add('cf7-has-shadow');
            shadowToggle.classList.add('cf7-active');
        }
    },

    updateTextShadow(property, value) {
        if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) return;

        const shadowToggle = this.container.querySelector('.cf7-btn-shadow');
        if (!shadowToggle || !shadowToggle.classList.contains('cf7-active')) return;

        // Store shadow properties on the element - CF7 Compatible
        if (!this.selectedElement.shadowConfig) {
            this.selectedElement.shadowConfig = {
                color: '#000000',
                blur: '2',
                offsetX: '2',
                offsetY: '2',
                opacity: '100'
            };
        }

        this.selectedElement.shadowConfig[property] = value;
        this.applyTextShadowToSelected();
    }
};
