/**
 * Font Manager - Billboard Maker
 * Font management methods
 * Part of CF7TextEditor system
 */

// These methods will be mixed into the CF7TextEditor class
export const fontManagerMethods = {
    updateSelectedFont(property, value) {
        if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) return;

        // Apply font styles to the editable content area
        const editableContent = this.selectedElement.querySelector('.cf7-editable-content');
        if (editableContent) {
            editableContent.style[property] = value;
        } else {
            // Fallback for older elements
            this.selectedElement.style[property] = value;
        }
    },

    toggleSelectedFont(property, activeValue, inactiveValue) {
        // Get the button that was clicked for visual feedback
        let button = null;
        if (property === 'fontWeight') button = this.fontControls.bold;
        if (property === 'fontStyle') button = this.fontControls.italic;

        // If no element is selected, just toggle button visual state
        if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) {
            if (button) {
                button.classList.toggle('cf7-active');
                console.log('No element selected, toggled button state:', button.classList.contains('cf7-active'));
            }
            return;
        }

        const editableContent = this.selectedElement.querySelector('.cf7-editable-content');
        const targetElement = editableContent || this.selectedElement;

        const currentValue = targetElement.style[property] ||
                           window.getComputedStyle(targetElement)[property];
        const newValue = (currentValue === activeValue) ? inactiveValue : activeValue;

        console.log(`Toggling ${property}: ${currentValue} -> ${newValue}`);
        targetElement.style[property] = newValue;

        // Don't call updateFontControlsFromElement as it might override our button state
        // Instead, manually update the button state
        if (button) {
            const isActive = (newValue === activeValue);
            button.classList.toggle('cf7-active', isActive);
            console.log(`Button ${property} active state:`, isActive);
        }
    },

    // CF7 Compatible: Set text alignment for selected text element
    setTextAlignment(alignment) {
        console.log('setTextAlignment called with:', alignment);
        console.log('Selected element:', this.selectedElement);

        // Get the alignment buttons for visual feedback
        const alignButtons = {
            left: this.fontControls.alignLeft,
            center: this.fontControls.alignCenter,
            right: this.fontControls.alignRight,
            justify: this.fontControls.alignJustify
        };

        console.log('Alignment buttons:', alignButtons);

        // If no element is selected, just update button visual state
        if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) {
            // Clear all alignment button active states
            Object.values(alignButtons).forEach(btn => {
                if (btn) btn.classList.remove('cf7-active');
            });
            // Set the clicked button as active
            if (alignButtons[alignment]) {
                alignButtons[alignment].classList.add('cf7-active');
            }
            return;
        }

        const editableContent = this.selectedElement.querySelector('.cf7-editable-content');
        const targetElement = editableContent || this.selectedElement;

        // Apply text alignment with !important to override any conflicting styles
        targetElement.style.setProperty('text-align', alignment, 'important');
        console.log(`Text alignment set to: ${alignment}`);

        // For justify, add additional properties
        if (alignment === 'justify') {
            targetElement.style.setProperty('text-align-last', 'left', 'important');
            targetElement.style.setProperty('word-spacing', 'normal', 'important');
            targetElement.style.setProperty('letter-spacing', 'normal', 'important');
            console.log('Justify alignment applied with additional properties');
        } else {
            // Remove justify-specific properties for other alignments
            targetElement.style.removeProperty('text-align-last');
        }

        console.log('Element computed textAlign:', window.getComputedStyle(targetElement).textAlign);
        console.log('Element style textAlign:', targetElement.style.textAlign);

        // Update button visual states - only one alignment can be active at a time
        console.log('Updating button states for alignment:', alignment);
        Object.entries(alignButtons).forEach(([align, btn]) => {
            if (btn) {
                const shouldBeActive = align === alignment;
                console.log(`Button ${align}: ${shouldBeActive ? 'ACTIVE' : 'inactive'}`);
                if (shouldBeActive) {
                    btn.classList.add('cf7-active');
                } else {
                    btn.classList.remove('cf7-active');
                }
            }
        });
    },

    // Debug method to test button toggle
    testBoldButtonToggle() {
        if (this.fontControls.bold) {
            console.log('Testing Bold button toggle...');
            console.log('Before toggle - has cf7-active:', this.fontControls.bold.classList.contains('cf7-active'));
            this.fontControls.bold.classList.toggle('cf7-active');
            console.log('After toggle - has cf7-active:', this.fontControls.bold.classList.contains('cf7-active'));
            console.log('Button classes:', this.fontControls.bold.className);
        } else {
            console.log('Bold button not found!');
        }
    },

    // Text Shadow Methods - Following CF7 patterns
    toggleTextShadow() {
        if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) return;

        const shadowToggle = this.container.querySelector('.cf7-btn-shadow');
        if (!shadowToggle) return;

        const isActive = shadowToggle.classList.contains('cf7-active');
        const editableContent = this.selectedElement.querySelector('.cf7-editable-content');
        const targetElement = editableContent || this.selectedElement;

        if (isActive) {
            // Remove text shadow
            targetElement.style.textShadow = 'none';
            this.selectedElement.classList.remove('cf7-has-shadow');
            shadowToggle.classList.remove('cf7-active');

            // Mark shadow as not applied
            if (this.selectedElement.shadowConfig) {
                this.selectedElement.shadowConfig.applied = false;
            }
        } else {
            // Apply text shadow with current settings
            this.applyTextShadowToSelected();
            this.selectedElement.classList.add('cf7-has-shadow');
            shadowToggle.classList.add('cf7-active');
        }
    },

    updateTextShadow(property, value) {
        if (!this.selectedElement || !this.selectedElement.classList.contains('cf7-draggable-text')) return;

        const shadowToggle = this.container.querySelector('.cf7-btn-shadow');
        if (!shadowToggle || !shadowToggle.classList.contains('cf7-active')) return;

        // Store shadow properties on the element - CF7 Compatible
        if (!this.selectedElement.shadowConfig) {
            this.selectedElement.shadowConfig = {
                color: '#000000',
                blur: '2',
                offsetX: '2',
                offsetY: '2',
                opacity: '100'
            };
        }

        this.selectedElement.shadowConfig[property] = value;
        this.applyTextShadowToSelected();
    },

    applyTextShadowToSelected() {
        if (!this.selectedElement || !this.selectedElement.shadowConfig) return;

        const config = this.selectedElement.shadowConfig;
        const editableContent = this.selectedElement.querySelector('.cf7-editable-content');
        const targetElement = editableContent || this.selectedElement;

        // Convert opacity percentage to decimal
        const opacity = parseInt(config.opacity || 100) / 100;

        // Create shadow string
        const shadowString = `${config.offsetX || 2}px ${config.offsetY || 2}px ${config.blur || 2}px rgba(0, 0, 0, ${opacity})`;

        targetElement.style.textShadow = shadowString;
        config.applied = true;
    },

    resetFontControls() {
        if (this.fontControls.fontFamily) this.fontControls.fontFamily.value = 'Arial, sans-serif';
        if (this.fontControls.fontSize) this.fontControls.fontSize.value = '16';
        if (this.fontControls.bold) this.fontControls.bold.classList.remove('cf7-active');
        if (this.fontControls.italic) this.fontControls.italic.classList.remove('cf7-active');
        if (this.fontControls.color) this.fontControls.color.value = '#000000';

        // Reset text alignment buttons
        if (this.fontControls.alignLeft) this.fontControls.alignLeft.classList.remove('cf7-active');
        if (this.fontControls.alignCenter) this.fontControls.alignCenter.classList.remove('cf7-active');
        if (this.fontControls.alignRight) this.fontControls.alignRight.classList.remove('cf7-active');
        if (this.fontControls.alignJustify) this.fontControls.alignJustify.classList.remove('cf7-active');

        // Reset text shadow controls to defaults
        const shadowColorPicker = this.container.querySelector('.cf7-color-picker[data-shadow="color"]');
        const shadowBlurInput = this.container.querySelector('.cf7-input-shadow-blur');
        const shadowOffsetXInput = this.container.querySelector('.cf7-input-shadow-offset[data-axis="x"]');
        const shadowOffsetYInput = this.container.querySelector('.cf7-input-shadow-offset[data-axis="y"]');

        if (shadowColorPicker) shadowColorPicker.value = '#000000';
        if (shadowBlurInput) shadowBlurInput.value = '2';
        if (shadowOffsetXInput) shadowOffsetXInput.value = '2';
        if (shadowOffsetYInput) shadowOffsetYInput.value = '2';

        // Only reset shadow button when no text is selected
        if (this.fontControls.textShadow) this.fontControls.textShadow.classList.remove('cf7-active');

        // Disable all font controls when no text is selected
        this.disableFontControls();
    },

    enableFontControls() {
        // Enable font family dropdown
        if (this.fontControls.fontFamily) {
            if (this.fontControls.fontFamily.classList.contains('cf7-font-preview-dropdown')) {
                this.fontControls.fontFamily.classList.remove('cf7-disabled');
                const button = this.fontControls.fontFamily.querySelector('.cf7-font-preview-button');
                if (button) button.disabled = false;
            } else {
                this.fontControls.fontFamily.disabled = false;
                this.fontControls.fontFamily.classList.remove('cf7-disabled');
            }
        }

        // Enable font size input
        if (this.fontControls.fontSize) {
            this.fontControls.fontSize.disabled = false;
            this.fontControls.fontSize.classList.remove('cf7-disabled');
        }

        // Enable style buttons
        if (this.fontControls.bold) {
            this.fontControls.bold.disabled = false;
            this.fontControls.bold.classList.remove('cf7-disabled');
        }
        if (this.fontControls.italic) {
            this.fontControls.italic.disabled = false;
            this.fontControls.italic.classList.remove('cf7-disabled');
        }

        // Enable alignment buttons
        [this.fontControls.alignLeft, this.fontControls.alignCenter,
         this.fontControls.alignRight, this.fontControls.alignJustify].forEach(btn => {
            if (btn) {
                btn.disabled = false;
                btn.classList.remove('cf7-disabled');
            }
        });

        // Enable color picker
        if (this.fontControls.color) {
            this.fontControls.color.disabled = false;
            this.fontControls.color.classList.remove('cf7-disabled');
        }
    },

    disableFontControls() {
        // Disable font family dropdown
        if (this.fontControls.fontFamily) {
            if (this.fontControls.fontFamily.classList.contains('cf7-font-preview-dropdown')) {
                this.fontControls.fontFamily.classList.add('cf7-disabled');
                const button = this.fontControls.fontFamily.querySelector('.cf7-font-preview-button');
                if (button) button.disabled = true;
            } else {
                this.fontControls.fontFamily.disabled = true;
                this.fontControls.fontFamily.classList.add('cf7-disabled');
            }
        }

        // Disable font size input
        if (this.fontControls.fontSize) {
            this.fontControls.fontSize.disabled = true;
            this.fontControls.fontSize.classList.add('cf7-disabled');
        }

        // Disable style buttons
        if (this.fontControls.bold) {
            this.fontControls.bold.disabled = true;
            this.fontControls.bold.classList.add('cf7-disabled');
        }
        if (this.fontControls.italic) {
            this.fontControls.italic.disabled = true;
            this.fontControls.italic.classList.add('cf7-disabled');
        }

        // Disable alignment buttons
        [this.fontControls.alignLeft, this.fontControls.alignCenter,
         this.fontControls.alignRight, this.fontControls.alignJustify].forEach(btn => {
            if (btn) {
                btn.disabled = true;
                btn.classList.add('cf7-disabled');
            }
        });

        // Disable color picker
        if (this.fontControls.color) {
            this.fontControls.color.disabled = true;
            this.fontControls.color.classList.add('cf7-disabled');
        }
    },

    rgbToHex(rgb) {
        // Convert RGB color to hex format
        if (rgb.startsWith('#')) return rgb;

        const rgbMatch = rgb.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
        if (rgbMatch) {
            const r = parseInt(rgbMatch[1]);
            const g = parseInt(rgbMatch[2]);
            const b = parseInt(rgbMatch[3]);
            return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
        }
        return '#000000';
    }
};
